"""
Shared API Authentication Manager for Shoonya Trading System

This module provides a centralized authentication system that maintains a single 
logged-in session across all Python files, eliminating the need for Jupyter-specific 
authentication while ensuring secure credential management.

Features:
- Singleton pattern for single API instance
- Automatic session management and reconnection
- Thread-safe operations
- Secure credential handling
- Session persistence and validation
"""

import os
import sys
import threading
import time
import pickle
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import logging

# Add the dist directory to Python path for NorenRestApi
current_dir = os.path.dirname(os.path.abspath(__file__))
dist_path = os.path.join(current_dir, '..', 'dist')
if dist_path not in sys.path:
    sys.path.insert(0, dist_path)

# Add parent directory to path for api_helper
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    from api_helper import ShoonyaApiPy
except ImportError:
    # Fallback to direct NorenApi import
    try:
        from NorenRestApiPy.NorenApi import NorenApi as ShoonyaApiPy
    except ImportError:
        try:
            from NorenApi import NorenApi as ShoonyaApiPy
        except ImportError:
            raise ImportError("Neither api_helper nor NorenRestApi found. Please ensure they're installed correctly.")

from secure_auth import SecureAuth

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', handlers=[logging.FileHandler('shared_api_manager.log', encoding='utf-8'), logging.StreamHandler(sys.stdout)])
logger = logging.getLogger(__name__)

class ShoonyaApiManager:
    """
    Singleton API Manager for Shoonya Trading System
    
    Provides centralized authentication and session management across all Python files.
    Ensures only one active session and handles automatic reconnection.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ShoonyaApiManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.api = None
        self.session_token = None
        self.credentials = None
        self.last_login_time = None
        self.session_file = os.path.join(os.path.dirname(__file__), '.session_cache')
        self.auth = SecureAuth()
        self._api_lock = threading.Lock()
        
        # Session validation settings
        self.session_timeout_hours = 8  # Shoonya sessions typically last 8-12 hours
        self.auto_reconnect = True
        
        logger.info("ShoonyaApiManager initialized")
    
    def get_api(self) -> ShoonyaApiPy:
        """
        Get the authenticated API instance
        
        Returns:
            NorenApi: Authenticated API instance
            
        Raises:
            Exception: If authentication fails
        """
        with self._api_lock:
            if not self._is_session_valid():
                logger.info("Session invalid or expired, attempting login...")
                self._login()
            
            if self.api is None:
                raise Exception("Failed to establish API connection")
                
            return self.api
    
    def _is_session_valid(self) -> bool:
        """
        Check if current session is valid
        
        Returns:
            bool: True if session is valid, False otherwise
        """
        if self.api is None or self.session_token is None:
            return False
            
        # Check session timeout
        if self.last_login_time:
            session_age = datetime.now() - self.last_login_time
            if session_age > timedelta(hours=self.session_timeout_hours):
                logger.warning("Session expired due to timeout")
                return False
        
        # Test API connection with a lightweight call
        try:
            # Check if session token and username are set in the API instance
            if not hasattr(self.api, '_NorenApi__susertoken') or not self.api._NorenApi__susertoken:
                logger.warning("Session validation failed - no session token in API instance")
                return False

            if not hasattr(self.api, '_NorenApi__username') or not self.api._NorenApi__username:
                logger.warning("Session validation failed - no username in API instance")
                return False

            # Use get_limits() as a lightweight test call
            result = self.api.get_limits()
            if result and result.get('stat') == 'Ok':
                return True
            else:
                logger.warning("Session validation failed - API test call unsuccessful")
                return False
        except Exception as e:
            logger.warning(f"Session validation failed: {str(e)}")
            return False
    
    def _login(self):
        """
        Perform login with secure credential handling
        """
        try:
            # Try to load cached session first
            if self._load_cached_session():
                if self._is_session_valid():
                    logger.info("Loaded valid cached session")
                    return
                else:
                    logger.info("Cached session invalid, performing fresh login")
            
            # Get credentials securely
            if not self.credentials:
                logger.info("Loading credentials...")
                self.credentials = self.auth.get_complete_credentials()
                
                if not self.credentials:
                    raise Exception("Failed to load credentials")
            
            # Initialize API using ShoonyaApiPy (which extends NorenApi)
            self.api = ShoonyaApiPy()
            
            # Perform login
            logger.info("Attempting login to Shoonya API...")
            login_result = self.api.login(
                userid=self.credentials['user'],
                password=self.credentials['pwd'],
                twoFA=self.credentials['factor2'],
                vendor_code=self.credentials['vc'],
                api_secret=self.credentials['apikey'],
                imei=self.credentials['imei']
            )
            
            if login_result and login_result.get('stat') == 'Ok':
                self.session_token = login_result.get('susertoken')
                self.last_login_time = datetime.now()

                # Verify that the login method set all required attributes
                if hasattr(self.api, '_NorenApi__susertoken') and self.api._NorenApi__susertoken:
                    logger.debug(f"✅ Session token set in API instance")
                else:
                    logger.warning("⚠️ Session token not set in API instance after login")

                if hasattr(self.api, '_NorenApi__username') and self.api._NorenApi__username:
                    logger.debug(f"✅ Username set in API instance: {self.api._NorenApi__username}")
                else:
                    logger.warning("⚠️ Username not set in API instance after login")

                # Cache the session
                self._save_session_cache()

                logger.info("Successfully logged in to Shoonya API")
                logger.info(f"User: {self.credentials['user']}")
                logger.info(f"Vendor Code: {self.credentials['vc']}")
                logger.info(f"Login Time: {self.last_login_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Validate the connection
                try:
                    if self.auth.validate_connection_test(self.api):
                        logger.info("API connection validated successfully")
                    else:
                        logger.warning("API connection validation failed")
                except Exception as e:
                    logger.warning(f"API connection validation error: {str(e)}")
                    
            else:
                error_msg = login_result.get('emsg', 'Unknown login error') if login_result else 'No response from server'
                raise Exception(f"Login failed: {error_msg}")
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            self.api = None
            self.session_token = None
            raise
    
    def _save_session_cache(self):
        """Save session information to cache file"""
        try:
            session_data = {
                'session_token': self.session_token,
                'last_login_time': self.last_login_time,
                'user': self.credentials['user'] if self.credentials else None
            }
            
            with open(self.session_file, 'wb') as f:
                pickle.dump(session_data, f)
                
            logger.debug("Session cached successfully")
            
        except Exception as e:
            logger.warning(f"Failed to cache session: {str(e)}")
    
    def _load_cached_session(self) -> bool:
        """
        Load session from cache file
        
        Returns:
            bool: True if session loaded successfully, False otherwise
        """
        try:
            if not os.path.exists(self.session_file):
                return False
                
            with open(self.session_file, 'rb') as f:
                session_data = pickle.load(f)
            
            self.session_token = session_data.get('session_token')
            self.last_login_time = session_data.get('last_login_time')
            
            if self.session_token and self.last_login_time:
                # Initialize API with cached session
                self.api = ShoonyaApiPy()

                # Set the session token and user info in the NorenApi instance
                # NorenApi requires these internal attributes for API calls
                user = session_data.get('user')
                if user:
                    self.api._NorenApi__susertoken = self.session_token
                    self.api._NorenApi__username = user
                    self.api._NorenApi__accountid = user

                    logger.debug(f"Session loaded from cache for user: {user}")
                    return True
                else:
                    logger.warning("No user info in cached session")
                    return False
                
        except Exception as e:
            logger.warning(f"Failed to load cached session: {str(e)}")
            
        return False
    
    def logout(self):
        """Logout and cleanup session"""
        try:
            if self.api:
                logout_result = self.api.logout()
                if logout_result and logout_result.get('stat') == 'Ok':
                    logger.info("👋 Successfully logged out")
                else:
                    logger.warning("⚠️ Logout may not have completed successfully")
            
            # Cleanup
            self.api = None
            self.session_token = None
            self.last_login_time = None
            
            # Remove cached session
            if os.path.exists(self.session_file):
                os.remove(self.session_file)
                logger.debug("🗑️ Session cache cleared")
                
        except Exception as e:
            logger.error(f"❌ Logout error: {str(e)}")
    
    def force_reconnect(self):
        """Force a fresh login by clearing current session"""
        logger.info("🔄 Forcing reconnection...")
        self.api = None
        self.session_token = None
        self.last_login_time = None
        
        if os.path.exists(self.session_file):
            os.remove(self.session_file)
        
        # Get fresh API instance
        return self.get_api()
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        Get current session information
        
        Returns:
            Dict containing session details
        """
        return {
            'is_logged_in': self.api is not None and self.session_token is not None,
            'session_token': self.session_token,
            'last_login_time': self.last_login_time.isoformat() if self.last_login_time else None,
            'session_age_minutes': (datetime.now() - self.last_login_time).total_seconds() / 60 if self.last_login_time else None,
            'user': self.credentials['user'] if self.credentials else None
        }

# Global instance for easy access
_api_manager = None

def get_api() -> ShoonyaApiPy:
    """
    Get the global authenticated API instance
    
    Returns:
        ShoonyaApiPy: Authenticated API instance
    """
    global _api_manager
    if _api_manager is None:
        _api_manager = ShoonyaApiManager()
    return _api_manager.get_api()

def get_manager() -> ShoonyaApiManager:
    """
    Get the global API manager instance
    
    Returns:
        ShoonyaApiManager: The API manager instance
    """
    global _api_manager
    if _api_manager is None:
        _api_manager = ShoonyaApiManager()
    return _api_manager

def logout():
    """Logout from the global API session"""
    global _api_manager
    if _api_manager:
        _api_manager.logout()

def force_reconnect() -> ShoonyaApiPy:
    """Force a fresh login and return new API instance"""
    global _api_manager
    if _api_manager is None:
        _api_manager = ShoonyaApiManager()
    return _api_manager.force_reconnect()

# Example usage and testing
if __name__ == "__main__":
    try:
        # Test the API manager
        print("🧪 Testing Shared API Manager...")
        
        # Get API instance
        api = get_api()
        print("✅ API instance obtained successfully")
        
        # Test API call
        limits = api.get_limits()
        if limits and limits.get('stat') == 'Ok':
            print("✅ API test call successful")
            print(f"👤 API connection validated")
        else:
            print("❌ API test call failed")
        
        # Get session info
        manager = get_manager()
        session_info = manager.get_session_info()
        print(f"📊 Session Info: {session_info}")
        
    except Exception as e:
        print(f"Test failed: {str(e)}")
